import React, { useState, useEffect } from 'react';
import { getDatabase, ref, onValue, update, remove } from 'firebase/database';
import { database } from '../../src/config/firebase';

const Alerts = () => {
  const [alerts, setAlerts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState('all'); // all, active, inactive

  useEffect(() => {
    const alertsRef = ref(database, 'alerts');
    
    const unsubscribe = onValue(alertsRef, (snapshot) => {
      const data = snapshot.val();
      if (data) {
        const alertsArray = Object.entries(data).map(([id, alert]) => ({
          id,
          ...alert
        })).sort((a, b) => b.createdAt - a.createdAt);
        
        setAlerts(alertsArray);
      } else {
        setAlerts([]);
      }
      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  const handleToggleAlert = async (alertId, currentStatus) => {
    try {
      const alertRef = ref(database, `alerts/${alertId}`);
      await update(alertRef, {
        isActive: !currentStatus,
        updatedAt: Date.now()
      });
    } catch (error) {
      console.error('Error toggling alert:', error);
      alert('Failed to update alert status');
    }
  };

  const handleDeleteAlert = async (alertId) => {
    if (window.confirm('Are you sure you want to delete this alert? This action cannot be undone.')) {
      try {
        const alertRef = ref(database, `alerts/${alertId}`);
        await remove(alertRef);
      } catch (error) {
        console.error('Error deleting alert:', error);
        alert('Failed to delete alert');
      }
    }
  };

  const filteredAlerts = alerts.filter(alert => {
    if (filter === 'active') return alert.isActive;
    if (filter === 'inactive') return !alert.isActive;
    return true;
  });

  const getSeverityColor = (severity) => {
    switch (severity?.toLowerCase()) {
      case 'critical': return 'red';
      case 'high': return 'orange';
      case 'medium': return 'yellow';
      case 'low': return 'green';
      default: return 'gray';
    }
  };

  const formatDate = (timestamp) => {
    return new Date(timestamp).toLocaleString();
  };

  if (loading) {
    return (
      <div className="alerts-page loading">
        <div className="loading-spinner">Loading alerts...</div>
      </div>
    );
  }

  return (
    <div className="alerts-page">
      <div className="page-header">
        <h2>Emergency Alerts Management</h2>
        <div className="page-actions">
          <select 
            value={filter} 
            onChange={(e) => setFilter(e.target.value)}
            className="filter-select"
          >
            <option value="all">All Alerts ({alerts.length})</option>
            <option value="active">Active ({alerts.filter(a => a.isActive).length})</option>
            <option value="inactive">Inactive ({alerts.filter(a => !a.isActive).length})</option>
          </select>
        </div>
      </div>

      <div className="alerts-list">
        {filteredAlerts.length > 0 ? (
          filteredAlerts.map((alert) => (
            <div key={alert.id} className={`alert-card ${alert.isActive ? 'active' : 'inactive'}`}>
              <div className="alert-header">
                <div className="alert-title-section">
                  <h3 className="alert-title">{alert.title}</h3>
                  <div className="alert-meta">
                    <span className={`severity-badge ${getSeverityColor(alert.severity)}`}>
                      {alert.severity?.toUpperCase() || 'UNKNOWN'}
                    </span>
                    <span className="alert-type">{alert.type}</span>
                    <span className={`status-badge ${alert.isActive ? 'active' : 'inactive'}`}>
                      {alert.isActive ? 'ACTIVE' : 'INACTIVE'}
                    </span>
                  </div>
                </div>
                <div className="alert-actions">
                  <button
                    className={`toggle-button ${alert.isActive ? 'deactivate' : 'activate'}`}
                    onClick={() => handleToggleAlert(alert.id, alert.isActive)}
                  >
                    {alert.isActive ? '⏸️ Deactivate' : '▶️ Activate'}
                  </button>
                  <button
                    className="delete-button"
                    onClick={() => handleDeleteAlert(alert.id)}
                  >
                    🗑️ Delete
                  </button>
                </div>
              </div>

              <div className="alert-content">
                <p className="alert-description">{alert.description}</p>
                
                {alert.location && (
                  <div className="alert-location">
                    <span className="location-icon">📍</span>
                    <span className="location-text">
                      {alert.location.address || `${alert.location.latitude}, ${alert.location.longitude}`}
                    </span>
                  </div>
                )}

                <div className="alert-details">
                  <div className="detail-item">
                    <span className="detail-label">Created:</span>
                    <span className="detail-value">{formatDate(alert.createdAt)}</span>
                  </div>
                  {alert.updatedAt && (
                    <div className="detail-item">
                      <span className="detail-label">Updated:</span>
                      <span className="detail-value">{formatDate(alert.updatedAt)}</span>
                    </div>
                  )}
                  {alert.expiresAt && (
                    <div className="detail-item">
                      <span className="detail-label">Expires:</span>
                      <span className="detail-value">{formatDate(alert.expiresAt)}</span>
                    </div>
                  )}
                  {alert.radius && (
                    <div className="detail-item">
                      <span className="detail-label">Radius:</span>
                      <span className="detail-value">{alert.radius} km</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="no-alerts">
            <div className="no-alerts-icon">🚨</div>
            <h3>No alerts found</h3>
            <p>
              {filter === 'active' 
                ? 'No active alerts at this time.' 
                : filter === 'inactive'
                ? 'No inactive alerts found.'
                : 'No alerts have been created yet.'}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default Alerts;

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: #f8fafc;
  color: #1e293b;
  line-height: 1.6;
}

.app {
  min-height: 100vh;
}

.app-container {
  display: flex;
  min-height: 100vh;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.page-container {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
}

/* Loading States */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

.loading-spinner {
  font-size: 1.1rem;
  color: #64748b;
}

/* Sidebar Styles */
.sidebar {
  width: 280px;
  background: linear-gradient(180deg, #1e40af 0%, #1e3a8a 100%);
  color: white;
  display: flex;
  flex-direction: column;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.sidebar-header {
  padding: 2rem 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.logo-icon {
  font-size: 2rem;
}

.logo-text {
  font-size: 1.5rem;
  font-weight: 700;
}

.logo-subtitle {
  font-size: 0.875rem;
  opacity: 0.8;
  margin-left: 2.75rem;
}

.sidebar-nav {
  flex: 1;
  padding: 1rem 0;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 1.5rem;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
  background: none;
  border: none;
  border-left: 3px solid transparent;
  width: 100%;
  text-align: left;
  cursor: pointer;
}

.nav-item:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.nav-item.active {
  background: rgba(255, 255, 255, 0.15);
  color: white;
  border-left-color: #fbbf24;
}

.nav-icon {
  font-size: 1.25rem;
  width: 1.5rem;
  text-align: center;
}

.nav-content {
  display: flex;
  flex-direction: column;
}

.nav-label {
  font-weight: 500;
  font-size: 0.95rem;
}

.nav-description {
  font-size: 0.8rem;
  opacity: 0.7;
}

.sidebar-footer {
  padding: 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-dot.online {
  background: #10b981;
  box-shadow: 0 0 6px rgba(16, 185, 129, 0.5);
}

/* Header Styles */
.header {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.page-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
}

.current-time {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #64748b;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.emergency-status .status-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-badge.normal {
  background: #dcfce7;
  color: #166534;
}

.user-menu {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.user-icon {
  font-size: 1.5rem;
}

.user-details {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-weight: 500;
  font-size: 0.9rem;
}

.user-role {
  font-size: 0.8rem;
  color: #64748b;
}

.logout-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 0.375rem;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background-color 0.2s;
}

.logout-button:hover {
  background: #dc2626;
}

/* Login Page Styles */
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem;
}

.login-card {
  background: white;
  border-radius: 1rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  padding: 3rem;
  width: 100%;
  max-width: 400px;
}

.login-header {
  text-align: center;
  margin-bottom: 2rem;
}

.login-header h1 {
  font-size: 2rem;
  font-weight: 700;
  color: #1e40af;
  margin-bottom: 0.5rem;
}

.login-header h2 {
  font-size: 1.1rem;
  color: #64748b;
  font-weight: 500;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 500;
  color: #374151;
}

.form-group input {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 1rem;
  transition: border-color 0.2s;
}

.form-group input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.login-button {
  padding: 0.875rem;
  background: #1e40af;
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.login-button:hover:not(:disabled) {
  background: #1e3a8a;
}

.login-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.error-message {
  background: #fef2f2;
  color: #dc2626;
  padding: 0.75rem;
  border-radius: 0.5rem;
  border: 1px solid #fecaca;
  font-size: 0.875rem;
}

.login-footer {
  margin-top: 2rem;
  text-align: center;
}

.login-footer p {
  font-size: 0.8rem;
  color: #6b7280;
}

/* Page Header Styles */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.page-header h2 {
  font-size: 1.875rem;
  font-weight: 600;
  color: #1e293b;
}

.page-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.filter-select {
  padding: 0.5rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  background: white;
  font-size: 0.875rem;
}

.add-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #059669;
  color: white;
  border: none;
  border-radius: 0.375rem;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background-color 0.2s;
}

.add-button:hover {
  background: #047857;
}

/* Card Styles */
.alert-card, .report-card, .shelter-card {
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 1rem;
  overflow: hidden;
  transition: box-shadow 0.2s;
}

.alert-card:hover, .report-card:hover, .shelter-card:hover {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.alert-card.inactive, .shelter-card.inactive {
  opacity: 0.7;
}

/* Status Badges */
.status-badge, .severity-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.active, .severity-badge.green {
  background: #dcfce7;
  color: #166534;
}

.status-badge.inactive, .severity-badge.gray {
  background: #f1f5f9;
  color: #475569;
}

.severity-badge.red {
  background: #fee2e2;
  color: #991b1b;
}

.severity-badge.orange {
  background: #fed7aa;
  color: #9a3412;
}

.severity-badge.yellow {
  background: #fef3c7;
  color: #92400e;
}

.severity-badge.blue {
  background: #dbeafe;
  color: #1e40af;
}

/* Button Styles */
.toggle-button, .delete-button, .status-button {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 0.375rem;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s;
}

.toggle-button.activate {
  background: #059669;
  color: white;
}

.toggle-button.deactivate {
  background: #f59e0b;
  color: white;
}

.delete-button {
  background: #ef4444;
  color: white;
}

.delete-button:hover {
  background: #dc2626;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
}

.modal-content {
  background: white;
  border-radius: 0.75rem;
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.modal-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #6b7280;
}

.modal-body {
  padding: 1.5rem;
}

/* Dashboard Styles */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.dashboard-header h2 {
  font-size: 1.875rem;
  font-weight: 600;
  color: #1e293b;
}

.last-updated {
  font-size: 0.875rem;
  color: #64748b;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  border-radius: 0.75rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 1rem;
  border-left: 4px solid;
}

.stat-card.red {
  border-left-color: #ef4444;
}

.stat-card.orange {
  border-left-color: #f59e0b;
}

.stat-card.blue {
  border-left-color: #3b82f6;
}

.stat-card.green {
  border-left-color: #10b981;
}

.stat-icon {
  font-size: 2.5rem;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
}

.stat-title {
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.25rem;
}

.stat-description {
  font-size: 0.875rem;
  color: #6b7280;
}

.dashboard-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
}

.recent-activity, .quick-actions {
  background: white;
  border-radius: 0.75rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.recent-activity h3, .quick-actions h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #1e293b;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 0.5rem;
}

.activity-icon {
  font-size: 1.25rem;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-weight: 500;
  color: #1e293b;
}

.activity-time {
  font-size: 0.875rem;
  color: #64748b;
}

.no-activity {
  text-align: center;
  color: #6b7280;
  padding: 2rem;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s;
  text-align: left;
}

.action-button:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.action-button.emergency {
  background: #fef2f2;
  border-color: #fecaca;
  color: #991b1b;
}

.action-button.emergency:hover {
  background: #fee2e2;
}

.action-icon {
  font-size: 1.25rem;
}

.action-text {
  font-weight: 500;
}

/* Alert Card Styles */
.alerts-list, .reports-list, .shelters-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.alert-header, .report-header, .shelter-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1.5rem;
  border-bottom: 1px solid #f1f5f9;
}

.alert-title-section, .report-title-section, .shelter-title-section {
  flex: 1;
}

.alert-title, .report-type, .shelter-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.5rem;
}

.alert-meta {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.alert-type {
  font-size: 0.875rem;
  color: #6b7280;
  background: #f1f5f9;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
}

.alert-actions, .shelter-actions {
  display: flex;
  gap: 0.5rem;
}

.alert-content, .report-content, .shelter-content {
  padding: 1.5rem;
}

.alert-description, .report-description {
  color: #374151;
  margin-bottom: 1rem;
  line-height: 1.6;
}

.alert-location, .report-location {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  color: #6b7280;
  font-size: 0.875rem;
}

.location-icon {
  font-size: 1rem;
}

.alert-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f1f5f9;
}

.detail-label {
  font-weight: 500;
  color: #6b7280;
}

.detail-value {
  color: #1e293b;
}

/* No Data States */
.no-alerts, .no-reports {
  text-align: center;
  padding: 4rem 2rem;
  color: #6b7280;
}

.no-alerts-icon, .no-reports-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.no-alerts h3, .no-reports h3 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: #374151;
}

/* Shelter Specific Styles */
.shelters-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-item {
  background: white;
  padding: 1.5rem;
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.stat-item .stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: #1e40af;
  display: block;
}

.stat-item .stat-label {
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

.shelter-info {
  margin-bottom: 1.5rem;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  color: #374151;
}

.info-icon {
  font-size: 1rem;
  color: #6b7280;
}

.occupancy-section {
  margin-bottom: 1.5rem;
}

.occupancy-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.occupancy-label {
  font-weight: 500;
  color: #374151;
}

.occupancy-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.occupancy-button {
  width: 2rem;
  height: 2rem;
  border: 1px solid #d1d5db;
  background: white;
  border-radius: 0.25rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
}

.occupancy-button:hover {
  background: #f9fafb;
}

.occupancy-display {
  font-weight: 600;
  color: #1e293b;
  min-width: 4rem;
  text-align: center;
}

.occupancy-bar {
  width: 100%;
  height: 0.5rem;
  background: #e5e7eb;
  border-radius: 0.25rem;
  overflow: hidden;
}

.occupancy-fill {
  height: 100%;
  transition: width 0.3s ease;
}

.occupancy-fill.low {
  background: #10b981;
}

.occupancy-fill.medium {
  background: #f59e0b;
}

.occupancy-fill.high {
  background: #ef4444;
}

.occupancy-fill.full {
  background: #991b1b;
}

.amenities-section {
  margin-top: 1rem;
}

.amenities-label {
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
  display: block;
}

.amenities-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.amenity-tag {
  background: #e0e7ff;
  color: #3730a3;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
}

/* Form Styles */
.shelter-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.amenities-checkboxes {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  margin: 0;
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 1rem;
}

.cancel-button, .submit-button {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.375rem;
  cursor: pointer;
  font-weight: 500;
}

.cancel-button {
  background: #f1f5f9;
  color: #374151;
}

.submit-button {
  background: #1e40af;
  color: white;
}

.submit-button:hover {
  background: #1e3a8a;
}

/* Report Modal Styles */
.report-detail-section {
  margin-bottom: 1.5rem;
}

.report-detail-section h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.75rem;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.contact-info p {
  margin-bottom: 0.5rem;
  color: #374151;
}

.status-buttons {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.status-button {
  padding: 0.5rem 1rem;
  border: 1px solid;
  border-radius: 0.375rem;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s;
}

.status-button.pending {
  background: #fef3c7;
  color: #92400e;
  border-color: #fcd34d;
}

.status-button.investigating {
  background: #dbeafe;
  color: #1e40af;
  border-color: #93c5fd;
}

.status-button.resolved {
  background: #dcfce7;
  color: #166534;
  border-color: #86efac;
}

.status-button.dismissed {
  background: #f1f5f9;
  color: #475569;
  border-color: #cbd5e1;
}

.status-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
  .app-container {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    height: auto;
  }

  .page-container {
    padding: 1rem;
  }

  .header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .page-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .dashboard-content {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .alert-header, .report-header, .shelter-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .alert-actions, .shelter-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .amenities-checkboxes {
    grid-template-columns: 1fr;
  }
}

/* Map Styles */
.emergency-map {
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  position: relative;
}

.map-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.map-error {
  border-radius: 0.75rem;
  border: 2px dashed #d1d5db;
  color: #6b7280;
  text-align: center;
}

.error-content p {
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.error-content small {
  font-size: 0.75rem;
  opacity: 0.7;
}

.map-overlay {
  position: absolute;
  top: 1rem;
  right: 1rem;
  z-index: 100;
}

.map-info-window {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 1rem;
  max-width: 300px;
  min-width: 200px;
}

.info-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.75rem;
}

.info-header h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  flex: 1;
}

.info-header .close-button {
  background: none;
  border: none;
  font-size: 1.25rem;
  cursor: pointer;
  color: #6b7280;
  padding: 0;
  margin-left: 0.5rem;
}

.info-content p {
  color: #374151;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.map-legend {
  position: absolute;
  bottom: 1rem;
  left: 1rem;
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 1rem;
  z-index: 100;
}

.map-legend h5 {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
  font-size: 0.8rem;
  color: #374151;
}

.legend-icon {
  font-size: 1rem;
}

/* Map Container for CreateAlert */
.map-container {
  height: 300px;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.location-coordinates {
  font-size: 0.875rem;
  color: #6b7280;
  text-align: center;
  padding: 0.5rem;
  background: #f9fafb;
  border-radius: 0.25rem;
}

/* Map Marker Styles */
.map-marker {
  transition: transform 0.2s ease;
}

.map-marker:hover {
  transform: translate(-50%, -100%) scale(1.1);
}

.marker-icon {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* Map Controls */
.map-controls {
  position: absolute;
  top: 1rem;
  left: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  z-index: 100;
}

.map-control-button {
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  padding: 0.5rem;
  cursor: pointer;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  transition: all 0.2s;
}

.map-control-button:hover {
  background: #f9fafb;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.map-control-button.active {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

/* Responsive Map Styles */
@media (max-width: 768px) {
  .map-overlay {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    right: auto;
  }

  .map-info-window {
    max-width: 90vw;
  }

  .map-legend {
    bottom: 0.5rem;
    left: 0.5rem;
    right: 0.5rem;
    padding: 0.75rem;
  }

  .legend-item {
    font-size: 0.75rem;
  }

  .map-controls {
    top: 0.5rem;
    left: 0.5rem;
  }
}

/* Maps Page Styles */
.maps-page {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 120px);
}

.map-toggles {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.toggle-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  cursor: pointer;
}

.toggle-label input[type="checkbox"] {
  margin: 0;
}

.map-section {
  flex: 1;
  position: relative;
}

.marker-details-panel {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 350px;
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  z-index: 200;
  max-height: calc(100% - 2rem);
  overflow-y: auto;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.panel-header h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.panel-content {
  padding: 1rem;
}

.alert-details h4,
.shelter-details h4,
.sos-details h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 1rem 0;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.75rem;
  gap: 1rem;
}

.detail-row .label {
  font-weight: 500;
  color: #6b7280;
  min-width: 80px;
  font-size: 0.875rem;
}

.detail-row .value {
  color: #1e293b;
  font-size: 0.875rem;
  text-align: right;
  flex: 1;
}

.contact-info {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #f1f5f9;
}

.contact-info h5,
.location-info h5 {
  font-size: 0.9rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.75rem 0;
}

.location-info {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #f1f5f9;
}

.external-link {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  color: #3b82f6;
  text-decoration: none;
  font-size: 0.875rem;
  margin-top: 0.5rem;
}

.external-link:hover {
  text-decoration: underline;
}

/* Responsive Maps Page */
@media (max-width: 768px) {
  .maps-page {
    height: calc(100vh - 80px);
  }

  .page-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .map-toggles {
    flex-wrap: wrap;
    gap: 0.75rem;
  }

  .marker-details-panel {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: auto;
    border-radius: 0;
    max-height: none;
    z-index: 1000;
  }

  .panel-content {
    padding-bottom: 2rem;
  }
}

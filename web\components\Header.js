import React, { useState, useEffect } from 'react';

const Header = ({ user, onLogout }) => {
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const formatTime = (date) => {
    return date.toLocaleString('en-US', {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  return (
    <header className="header">
      <div className="header-left">
        <h1 className="page-title">Emergency Management Dashboard</h1>
        <div className="current-time">
          <span className="time-icon">🕐</span>
          <span className="time-text">{formatTime(currentTime)}</span>
        </div>
      </div>

      <div className="header-right">
        <div className="emergency-status">
          <div className="status-badge normal">
            <span className="status-icon">✅</span>
            <span className="status-text">Normal Operations</span>
          </div>
        </div>

        <div className="user-menu">
          <div className="user-info">
            <span className="user-icon">👤</span>
            <div className="user-details">
              <span className="user-name">
                {user?.displayName || user?.email || 'Admin User'}
              </span>
              <span className="user-role">Emergency Coordinator</span>
            </div>
          </div>
          
          <button 
            className="logout-button"
            onClick={onLogout}
            title="Logout"
          >
            <span className="logout-icon">🚪</span>
            <span className="logout-text">Logout</span>
          </button>
        </div>
      </div>
    </header>
  );
};

export default Header;

const { initializeApp } = require('firebase/app');
const { getDatabase, ref, set } = require('firebase/database');
const { getFirestore, collection, addDoc, doc, setDoc } = require('firebase/firestore');

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyD9yWrY2xO5oS59_mEaGthe5VnAtDtWpAM",
  authDomain: "safehaven-463909.firebaseapp.com",
  projectId: "safehaven-463909",
  storageBucket: "safehaven-463909.appspot.com",
  messagingSenderId: "441114248968",
  appId: "1:441114248968:web:d4f1d612fa335733380ebd",
  measurementId: "G-WJN6VFZ3CR",
  databaseURL: "https://safehaven-463909-default-rtdb.firebaseio.com"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const database = getDatabase(app);
const firestore = getFirestore(app);

async function addSampleData() {
  try {
    console.log('Adding sample data to Firebase...');

    // Sample Alerts for Realtime Database
    const alertsData = {
      alert1: {
        id: 'alert1',
        title: 'Flood Warning - Downtown Area',
        description: 'Heavy rainfall has caused flooding in the downtown area. Residents are advised to avoid low-lying areas and seek higher ground.',
        type: 'natural_disaster',
        severity: 'high',
        location: {
          latitude: 37.7749,
          longitude: -122.4194,
          address: 'Downtown San Francisco, CA'
        },
        createdAt: Date.now(),
        expiresAt: Date.now() + (24 * 60 * 60 * 1000), // 24 hours from now
        createdBy: 'emergency-coordinator',
        isActive: true,
        radius: 5
      },
      alert2: {
        id: 'alert2',
        title: 'Wildfire Alert - North Hills',
        description: 'A wildfire has been reported in the North Hills area. Evacuation orders are in effect for zones A and B.',
        type: 'fire',
        severity: 'critical',
        location: {
          latitude: 37.8044,
          longitude: -122.2711,
          address: 'North Hills, Oakland, CA'
        },
        createdAt: Date.now() - (2 * 60 * 60 * 1000), // 2 hours ago
        expiresAt: Date.now() + (48 * 60 * 60 * 1000), // 48 hours from now
        createdBy: 'fire-department',
        isActive: true,
        radius: 10
      },
      alert3: {
        id: 'alert3',
        title: 'Medical Emergency - Hospital Capacity',
        description: 'Local hospitals are at capacity. Non-emergency cases should seek alternative care.',
        type: 'medical',
        severity: 'medium',
        location: {
          latitude: 37.7849,
          longitude: -122.4094,
          address: 'Mission District, San Francisco, CA'
        },
        createdAt: Date.now() - (30 * 60 * 1000), // 30 minutes ago
        expiresAt: Date.now() + (12 * 60 * 60 * 1000), // 12 hours from now
        createdBy: 'health-department',
        isActive: true,
        radius: 3
      }
    };

    // Add alerts to Realtime Database
    await set(ref(database, 'alerts'), alertsData);
    console.log('✅ Sample alerts added to Realtime Database');

    // Sample Shelters for Firestore
    const sheltersData = [
      {
        name: 'Community Center Emergency Shelter',
        address: '123 Main St, San Francisco, CA 94102',
        location: {
          latitude: 37.7849,
          longitude: -122.4194
        },
        capacity: 150,
        currentOccupancy: 45,
        type: 'emergency',
        amenities: ['Food Service', 'Medical Care', 'Pet Friendly', 'Wheelchair Accessible', 'WiFi'],
        contactPhone: '+1-************',
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        name: 'Red Cross Evacuation Center',
        address: '456 Oak Ave, Oakland, CA 94601',
        location: {
          latitude: 37.8044,
          longitude: -122.2711
        },
        capacity: 200,
        currentOccupancy: 78,
        type: 'evacuation',
        amenities: ['Food Service', 'Medical Care', 'Showers', 'Laundry', 'Childcare', 'Security'],
        contactPhone: '******-555-0456',
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        name: 'School Gymnasium Shelter',
        address: '789 Pine St, Berkeley, CA 94704',
        location: {
          latitude: 37.8715,
          longitude: -122.2730
        },
        capacity: 100,
        currentOccupancy: 23,
        type: 'temporary',
        amenities: ['Food Service', 'Wheelchair Accessible', 'Parking'],
        contactPhone: '******-555-0789',
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ];

    // Add shelters to Firestore
    for (const shelter of sheltersData) {
      await addDoc(collection(firestore, 'shelters'), shelter);
    }
    console.log('✅ Sample shelters added to Firestore');

    // Sample SOS Messages for Firestore
    const sosMessagesData = [
      {
        message: 'Trapped in building due to flooding, need immediate rescue',
        location: {
          latitude: 37.7749,
          longitude: -122.4194
        },
        contactInfo: {
          name: 'John Doe',
          phone: '******-555-1234'
        },
        status: 'pending',
        priority: 'high',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        message: 'Elderly person needs medical assistance, cannot evacuate',
        location: {
          latitude: 37.8044,
          longitude: -122.2711
        },
        contactInfo: {
          name: 'Jane Smith',
          phone: '******-555-5678'
        },
        status: 'investigating',
        priority: 'high',
        createdAt: new Date(Date.now() - 15 * 60 * 1000).toISOString(), // 15 minutes ago
        updatedAt: new Date().toISOString()
      },
      {
        message: 'Family of 4 stranded, car broke down during evacuation',
        location: {
          latitude: 37.7849,
          longitude: -122.4094
        },
        contactInfo: {
          name: 'Mike Johnson',
          phone: '******-555-9012'
        },
        status: 'pending',
        priority: 'medium',
        createdAt: new Date(Date.now() - 45 * 60 * 1000).toISOString(), // 45 minutes ago
        updatedAt: new Date().toISOString()
      }
    ];

    // Add SOS messages to Firestore
    for (const sosMessage of sosMessagesData) {
      await addDoc(collection(firestore, 'sosMessages'), sosMessage);
    }
    console.log('✅ Sample SOS messages added to Firestore');

    // Sample Incident Reports for Firestore
    const reportsData = [
      {
        type: 'Infrastructure Damage',
        description: 'Bridge on Highway 101 has structural damage due to flooding. Traffic is being diverted.',
        location: {
          latitude: 37.7849,
          longitude: -122.4194,
          address: 'Highway 101 Bridge, San Francisco, CA'
        },
        contactInfo: {
          name: 'Highway Patrol',
          phone: '******-555-0101',
          email: '<EMAIL>'
        },
        status: 'investigating',
        priority: 'high',
        createdAt: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(), // 3 hours ago
        updatedAt: new Date().toISOString()
      },
      {
        type: 'Power Outage',
        description: 'Widespread power outage affecting approximately 5,000 residents in the Mission District.',
        location: {
          latitude: 37.7849,
          longitude: -122.4094,
          address: 'Mission District, San Francisco, CA'
        },
        contactInfo: {
          name: 'PG&E Emergency',
          phone: '******-743-5000'
        },
        status: 'resolved',
        priority: 'medium',
        createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(), // 6 hours ago
        updatedAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString() // 1 hour ago
      },
      {
        type: 'Water Contamination',
        description: 'Potential water contamination reported in the downtown area. Residents advised to boil water.',
        location: {
          latitude: 37.7749,
          longitude: -122.4194,
          address: 'Downtown San Francisco, CA'
        },
        contactInfo: {
          name: 'Water Department',
          phone: '******-555-0200',
          email: '<EMAIL>'
        },
        status: 'pending',
        priority: 'high',
        createdAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(), // 1 hour ago
        updatedAt: new Date().toISOString()
      }
    ];

    // Add reports to Firestore
    for (const report of reportsData) {
      await addDoc(collection(firestore, 'reports'), report);
    }
    console.log('✅ Sample incident reports added to Firestore');

    console.log('🎉 All sample data has been successfully added to Firebase!');
    console.log('\nSample data includes:');
    console.log('- 3 Emergency alerts (Realtime Database)');
    console.log('- 3 Emergency shelters (Firestore)');
    console.log('- 3 SOS messages (Firestore)');
    console.log('- 3 Incident reports (Firestore)');

  } catch (error) {
    console.error('❌ Error adding sample data:', error);
  }
}

// Run the script
addSampleData();

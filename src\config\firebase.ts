import { initializeApp } from "firebase/app";
import { getAnalytics } from "firebase/analytics";
import { getAuth } from "firebase/auth";
import { getFirestore } from "firebase/firestore";
import { getDatabase } from "firebase/database";
import { getStorage } from "firebase/storage";
import { getMessaging, isSupported } from "firebase/messaging";
import Constants from 'expo-constants';
import { Platform } from 'react-native';

// Get environment variables from Expo Constants or process.env
const getEnvVar = (key: string) => {
  if (Platform.OS === 'web') {
    return process.env[key];
  }
  return Constants.expoConfig?.extra?.[key] || process.env[key];
};

// Your web app's Firebase configuration
const firebaseConfig = {
  apiKey: getEnvVar('EXPO_PUBLIC_FIREBASE_API_KEY'),
  authDomain: getEnvVar('EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN'),
  projectId: getEnvVar('EXPO_PUBLIC_FIREBASE_PROJECT_ID') || "safehaven-463909",
  storageBucket: getEnvVar('EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET'),
  messagingSenderId: getEnvVar('EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID'),
  appId: getEnvVar('EXPO_PUBLIC_FIREBASE_APP_ID'),
  measurementId: getEnvVar('EXPO_PUBLIC_FIREBASE_MEASUREMENT_ID'),
  databaseURL: getEnvVar('EXPO_PUBLIC_FIREBASE_DATABASE_URL')
};

// Validate that essential Firebase config values are present
if (!firebaseConfig.apiKey || !firebaseConfig.authDomain || !firebaseConfig.projectId) {
  console.error("Firebase configuration is missing or incomplete. Check your environment variables.");
  // Optionally, throw an error or render a fallback UI if running in a context where this is critical
}

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const analytics = getAnalytics(app);
const auth = getAuth(app);
const firestore = getFirestore(app);
const database = getDatabase(app);
const storage = getStorage(app);

// Initialize Firebase Cloud Messaging (only on web)
let messaging = null;
if (typeof window !== 'undefined') {
  isSupported().then(isSupported => {
    if (isSupported) {
      messaging = getMessaging(app);
    }
  });
}

export { app, analytics, auth, firestore, database, storage, messaging };

{"name": "safehaven", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:web": "expo export:web", "build:android": "expo build:android", "build:ios": "expo build:ios", "eject": "expo eject", "lint": "eslint .", "test": "jest", "prepare-functions": "cd functions && npm install", "deploy:hosting": "firebase deploy --only hosting", "deploy:functions": "firebase deploy --only functions", "deploy:all": "firebase deploy"}, "dependencies": {"@firebasegen/default-connector": "file:dataconnect-generated/js/default-connector", "@react-native-async-storage/async-storage": "^1.24.0", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/native": "^7.1.6", "@react-navigation/stack": "^7.2.10", "expo": "~52.0.46", "expo-firebase-recaptcha": "^1.4.4", "expo-image-picker": "^16.0.6", "expo-location": "^18.0.10", "expo-notifications": "^0.29.14", "expo-status-bar": "~2.0.1", "firebase": "^11.6.1", "google-map-react": "^2.2.1", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.9", "react-native-dotenv": "^3.4.11", "react-native-elements": "^3.4.3", "react-native-gesture-handler": "^2.25.0", "react-native-maps": "^1.22.6", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "^4.10.0", "react-router-dom": "^7.5.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/google-map-react": "^2.1.10", "@types/react": "~18.3.12", "eslint": "^8.56.0", "eslint-config-universe": "^12.0.0", "jest": "^29.7.0", "jest-expo": "^52.0.0", "typescript": "^5.3.3"}, "private": true}
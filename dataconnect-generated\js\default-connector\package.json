{"name": "@firebasegen/default-connector", "version": "1.0.0", "author": "Firebase <<EMAIL>> (https://firebase.google.com/)", "description": "Generated SDK For default", "license": "Apache-2.0", "engines": {"node": " >=18.0"}, "typings": "index.d.ts", "module": "esm/index.esm.js", "main": "index.cjs.js", "browser": "esm/index.esm.js", "exports": {".": {"types": "./index.d.ts", "require": "./index.cjs.js", "default": "./esm/index.esm.js"}, "./react": {"types": "./react/index.d.ts", "require": "./react/index.cjs.js", "import": "./react/esm/index.esm.js", "default": "./react/esm/index.esm.js"}, "./angular": {"types": "./angular/index.d.ts", "require": "./angular/index.cjs.js", "import": "./angular/esm/index.esm.js", "default": "./angular/esm/index.esm.js"}, "./package.json": "./package.json"}, "peerDependencies": {"firebase": "^11.3.0", "@tanstack-query-firebase/react": "^2.0.0", "@tanstack-query-firebase/angular": "^1.0.0"}}
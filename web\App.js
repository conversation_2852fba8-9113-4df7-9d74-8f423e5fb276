import React, { useState, useEffect } from 'react';
import { getAuth, onAuthStateChanged, signInWithEmailAndPassword, signOut } from 'firebase/auth';
import { app } from '../src/config/firebase';

// Components
import Sidebar from './components/Sidebar';
import Header from './components/Header';
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';
import Alerts from './pages/Alerts';
import SOSMessages from './pages/SOSMessages';
import Reports from './pages/Reports';
import Shelters from './pages/Shelters';
import CreateAlert from './pages/CreateAlert';
import Maps from './pages/Maps';

// Initialize Firebase Auth
const auth = getAuth(app);

const App = () => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState('dashboard');

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (currentUser) => {
      setUser(currentUser);
      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  const handleLogin = async (email, password) => {
    try {
      await signInWithEmailAndPassword(auth, email, password);
      setCurrentPage('dashboard');
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  };

  const handleLogout = async () => {
    try {
      await signOut(auth);
      setCurrentPage('login');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const renderCurrentPage = () => {
    switch (currentPage) {
      case 'dashboard':
        return <Dashboard />;
      case 'alerts':
        return <Alerts />;
      case 'sos-messages':
        return <SOSMessages />;
      case 'reports':
        return <Reports />;
      case 'shelters':
        return <Shelters />;
      case 'maps':
        return <Maps />;
      case 'create-alert':
        return <CreateAlert onNavigate={setCurrentPage} />;
      default:
        return <Dashboard />;
    }
  };

  if (loading) {
    return <div className="loading">Loading...</div>;
  }

  return (
    <div className="app">
      {user ? (
        <div className="app-container">
          <Sidebar currentPage={currentPage} onNavigate={setCurrentPage} />
          <div className="main-content">
            <Header user={user} onLogout={handleLogout} />
            <div className="page-container">
              {renderCurrentPage()}
            </div>
          </div>
        </div>
      ) : (
        <Login onLogin={handleLogin} />
      )}
    </div>
  );
};

export default App;

import React, { useState, useEffect } from 'react';
import { getDatabase, ref, onValue, off } from 'firebase/database';
import { collection, getDocs, query, where, orderBy, limit } from 'firebase/firestore';
import { firestore, database } from '../../src/config/firebase';

const Dashboard = () => {
  const [stats, setStats] = useState({
    activeAlerts: 0,
    sosMessages: 0,
    totalReports: 0,
    activeShelters: 0
  });
  const [recentActivity, setRecentActivity] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadDashboardData = async () => {
      try {
        // Load stats from Firebase
        await Promise.all([
          loadActiveAlerts(),
          loadSOSMessages(),
          loadReports(),
          loadShelters(),
          loadRecentActivity()
        ]);
      } catch (error) {
        console.error('Error loading dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadDashboardData();
  }, []);

  const loadActiveAlerts = async () => {
    const alertsRef = ref(database, 'alerts');
    onValue(alertsRef, (snapshot) => {
      const data = snapshot.val();
      if (data) {
        const activeCount = Object.values(data).filter(alert => alert.isActive).length;
        setStats(prev => ({ ...prev, activeAlerts: activeCount }));
      }
    });
  };

  const loadSOSMessages = async () => {
    try {
      const sosQuery = query(
        collection(firestore, 'sosMessages'),
        where('status', '==', 'pending'),
        orderBy('createdAt', 'desc')
      );
      const snapshot = await getDocs(sosQuery);
      setStats(prev => ({ ...prev, sosMessages: snapshot.size }));
    } catch (error) {
      console.error('Error loading SOS messages:', error);
    }
  };

  const loadReports = async () => {
    try {
      const reportsQuery = query(
        collection(firestore, 'reports'),
        orderBy('createdAt', 'desc')
      );
      const snapshot = await getDocs(reportsQuery);
      setStats(prev => ({ ...prev, totalReports: snapshot.size }));
    } catch (error) {
      console.error('Error loading reports:', error);
    }
  };

  const loadShelters = async () => {
    try {
      const sheltersQuery = query(
        collection(firestore, 'shelters'),
        where('isActive', '==', true)
      );
      const snapshot = await getDocs(sheltersQuery);
      setStats(prev => ({ ...prev, activeShelters: snapshot.size }));
    } catch (error) {
      console.error('Error loading shelters:', error);
    }
  };

  const loadRecentActivity = async () => {
    try {
      const activities = [];
      
      // Get recent alerts
      const alertsRef = ref(database, 'alerts');
      onValue(alertsRef, (snapshot) => {
        const data = snapshot.val();
        if (data) {
          const recentAlerts = Object.entries(data)
            .map(([id, alert]) => ({
              id,
              type: 'alert',
              title: alert.title,
              timestamp: alert.createdAt,
              severity: alert.severity
            }))
            .sort((a, b) => b.timestamp - a.timestamp)
            .slice(0, 5);
          
          setRecentActivity(recentAlerts);
        }
      });
    } catch (error) {
      console.error('Error loading recent activity:', error);
    }
  };

  const StatCard = ({ title, value, icon, color, description }) => (
    <div className={`stat-card ${color}`}>
      <div className="stat-icon">{icon}</div>
      <div className="stat-content">
        <div className="stat-value">{value}</div>
        <div className="stat-title">{title}</div>
        <div className="stat-description">{description}</div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="dashboard loading">
        <div className="loading-spinner">Loading dashboard...</div>
      </div>
    );
  }

  return (
    <div className="dashboard">
      <div className="dashboard-header">
        <h2>Emergency Management Overview</h2>
        <div className="last-updated">
          Last updated: {new Date().toLocaleTimeString()}
        </div>
      </div>

      <div className="stats-grid">
        <StatCard
          title="Active Alerts"
          value={stats.activeAlerts}
          icon="🚨"
          color="red"
          description="Current emergency alerts"
        />
        <StatCard
          title="SOS Messages"
          value={stats.sosMessages}
          icon="🆘"
          color="orange"
          description="Pending emergency requests"
        />
        <StatCard
          title="Total Reports"
          value={stats.totalReports}
          icon="📋"
          color="blue"
          description="Incident reports filed"
        />
        <StatCard
          title="Active Shelters"
          value={stats.activeShelters}
          icon="🏠"
          color="green"
          description="Emergency shelters available"
        />
      </div>

      <div className="dashboard-content">
        <div className="recent-activity">
          <h3>Recent Activity</h3>
          <div className="activity-list">
            {recentActivity.length > 0 ? (
              recentActivity.map((activity) => (
                <div key={activity.id} className="activity-item">
                  <div className="activity-icon">
                    {activity.type === 'alert' ? '🚨' : '📋'}
                  </div>
                  <div className="activity-content">
                    <div className="activity-title">{activity.title}</div>
                    <div className="activity-time">
                      {new Date(activity.timestamp).toLocaleString()}
                    </div>
                  </div>
                  {activity.severity && (
                    <div className={`severity-badge ${activity.severity}`}>
                      {activity.severity.toUpperCase()}
                    </div>
                  )}
                </div>
              ))
            ) : (
              <div className="no-activity">No recent activity</div>
            )}
          </div>
        </div>

        <div className="quick-actions">
          <h3>Quick Actions</h3>
          <div className="action-buttons">
            <button className="action-button emergency">
              <span className="action-icon">🚨</span>
              <span className="action-text">Create Emergency Alert</span>
            </button>
            <button className="action-button">
              <span className="action-icon">📋</span>
              <span className="action-text">View All Reports</span>
            </button>
            <button className="action-button">
              <span className="action-icon">🏠</span>
              <span className="action-text">Manage Shelters</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;

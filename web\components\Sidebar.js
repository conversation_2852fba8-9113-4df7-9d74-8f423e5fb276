import React from 'react';
import { Link, useLocation } from 'react-router-dom';

const Sidebar = () => {
  const location = useLocation();

  const menuItems = [
    {
      path: '/dashboard',
      icon: '📊',
      label: 'Dashboard',
      description: 'Overview & Statistics'
    },
    {
      path: '/alerts',
      icon: '🚨',
      label: 'Active Alerts',
      description: 'Manage Disaster Alerts'
    },
    {
      path: '/sos-messages',
      icon: '🆘',
      label: 'SOS Messages',
      description: 'Emergency Requests'
    },
    {
      path: '/reports',
      icon: '📋',
      label: 'Reports',
      description: 'Incident Reports'
    },
    {
      path: '/shelters',
      icon: '🏠',
      label: 'Shelters',
      description: 'Emergency Shelters'
    },
    {
      path: '/maps',
      icon: '🗺️',
      label: 'Maps',
      description: 'Emergency Response Map'
    },
    {
      path: '/create-alert',
      icon: '➕',
      label: 'Create Alert',
      description: 'New Emergency Alert'
    }
  ];

  return (
    <div className="sidebar">
      <div className="sidebar-header">
        <div className="logo">
          <span className="logo-icon">🛡️</span>
          <span className="logo-text">SafeHaven</span>
        </div>
        <div className="logo-subtitle">Emergency Management</div>
      </div>

      <nav className="sidebar-nav">
        {menuItems.map((item) => (
          <Link
            key={item.path}
            to={item.path}
            className={`nav-item ${location.pathname === item.path ? 'active' : ''}`}
          >
            <span className="nav-icon">{item.icon}</span>
            <div className="nav-content">
              <span className="nav-label">{item.label}</span>
              <span className="nav-description">{item.description}</span>
            </div>
          </Link>
        ))}
      </nav>

      <div className="sidebar-footer">
        <div className="status-indicator">
          <span className="status-dot online"></span>
          <span className="status-text">System Online</span>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;

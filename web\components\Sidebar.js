import React from 'react';

const Sidebar = ({ currentPage, onNavigate }) => {

  const menuItems = [
    {
      page: 'dashboard',
      icon: '📊',
      label: 'Dashboard',
      description: 'Overview & Statistics'
    },
    {
      page: 'alerts',
      icon: '🚨',
      label: 'Active Alerts',
      description: 'Manage Disaster Alerts'
    },
    {
      page: 'sos-messages',
      icon: '🆘',
      label: 'SOS Messages',
      description: 'Emergency Requests'
    },
    {
      page: 'reports',
      icon: '📋',
      label: 'Reports',
      description: 'Incident Reports'
    },
    {
      page: 'shelters',
      icon: '🏠',
      label: 'Shelters',
      description: 'Emergency Shelters'
    },
    {
      page: 'maps',
      icon: '🗺️',
      label: 'Maps',
      description: 'Emergency Response Map'
    },
    {
      page: 'create-alert',
      icon: '➕',
      label: 'Create Alert',
      description: 'New Emergency Alert'
    }
  ];

  return (
    <div className="sidebar">
      <div className="sidebar-header">
        <div className="logo">
          <span className="logo-icon">🛡️</span>
          <span className="logo-text">SafeHaven</span>
        </div>
        <div className="logo-subtitle">Emergency Management</div>
      </div>

      <nav className="sidebar-nav">
        {menuItems.map((item) => (
          <button
            key={item.page}
            onClick={() => onNavigate(item.page)}
            className={`nav-item ${currentPage === item.page ? 'active' : ''}`}
          >
            <span className="nav-icon">{item.icon}</span>
            <div className="nav-content">
              <span className="nav-label">{item.label}</span>
              <span className="nav-description">{item.description}</span>
            </div>
          </button>
        ))}
      </nav>

      <div className="sidebar-footer">
        <div className="status-indicator">
          <span className="status-dot online"></span>
          <span className="status-text">System Online</span>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;

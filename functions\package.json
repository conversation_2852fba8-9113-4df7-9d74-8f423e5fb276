{"name": "safehaven-functions", "description": "Cloud Functions for SafeHaven Disaster Management App", "scripts": {"serve": "firebase emulators:start --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "18"}, "main": "index.js", "dependencies": {"firebase-admin": "^13.3.0", "firebase-functions": "^6.3.2", "twilio": "^4.11.0"}, "devDependencies": {"firebase-functions-test": "^3.1.0"}, "private": true, "version": "1.0.0", "keywords": [], "author": "", "license": "ISC"}